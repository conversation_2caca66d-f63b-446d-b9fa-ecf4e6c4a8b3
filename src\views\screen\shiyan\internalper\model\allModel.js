import * as THREE from 'three';
import {initMotor} from '../../motor.js';
import {initWall} from '../../wall.js';
import {initZouxiangjia} from '../../zouxiangjia.js';
import {initBanka} from '../../banka.js';

export async function initModel(RmCabinetModelList,RmRoomList){
    const model = new THREE.Group();
    model.add(initWall(RmRoomList));
    model.add(initMotor(RmCabinetModelList));

    // 等待走向架模型异步加载完成
    const zouxiangjiaGroup = await initZouxiangjia(RmRoomList);
    model.add(zouxiangjiaGroup); // 添加走向架模型

    // model.add(initBanka());
    return model;
}