import * as THREE from 'three';
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import scene from './motor';

export function initWall(RmRoomList){
    const width = RmRoomList[0].length;//14.916m
    let length = Number(RmRoomList[0].width);//8.3m
    const height = RmRoomList[0].height;//4m  
    

    const group = new THREE.Group();
    group.name = '邯_C_丛台区碧桂园机房';
    const qiangGeometry1 = new THREE.BoxGeometry( length+0.1, height ,0.1 );
    
    // const qiangtexture = new THREE.TextureLoader().load( './pic/木头墙.png' );
    const sideMaterial = new THREE.MeshBasicMaterial( { 
        color:0xcccccc, // 基础颜色
        // roughness: 0.5, // 粗糙度（0 表示完全光滑，1 表示完全粗糙）
        // metalness: 0.0, // 金属度（0 表示非金属，1 表示金属）
        // clearcoat: 0.365, // 清漆层（模拟表面涂层）
        // clearcoatRoughness: 0.525, // 清漆层的粗糙度
        // reflectivity: 0.8, // 反射率
        side: THREE.DoubleSide, // 双面渲染
     } );
     const topMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff,
        side: THREE.DoubleSide,
    });

    const materials = [
        sideMaterial, // 右
        sideMaterial, // 左
        topMaterial,  // 顶
        sideMaterial, // 底
        sideMaterial, // 前
        sideMaterial  // 后
    ];
    //  const gui = new GUI();
    //  gui.addColor(sideMaterial,'color').name("墙颜色").onChange(function(value){
    //     sideMaterial.color.set(value);
    // });
    const qiang = new THREE.Mesh( qiangGeometry1, materials );
    qiang.castShadow = true; // 允许投射阴影
    qiang.receiveShadow = true; // 允许接收阴影
    qiang.translateX(length/2);
    qiang.translateY(height/2);
    const qiang2 = qiang.clone();
    qiang2.translateZ(width);
    const qiangGeometry2 = new THREE.BoxGeometry( width, height ,0.1);
    const qiang3 = new THREE.Mesh( qiangGeometry2, materials );
    qiang3.rotateY(Math.PI / 2);
    qiang3.translateZ(length);
    qiang3.translateX(-width/2);
    qiang3.translateY(height/2);
    const qiang4 = qiang3.clone();
    qiang4.translateZ(-length);
    group.add(qiang,qiang2,qiang3,qiang4);
    
    
    
    
    const floorGeometry = new THREE.PlaneGeometry( length, width );
    const floortexture = new THREE.TextureLoader().load( require('./pic/cizhuan.jpg') );
    const floorMaterial = new THREE.MeshBasicMaterial( { 
        map: floortexture,
        side: THREE.DoubleSide,
        color : 0xffffff,
     } );
     const floor = new THREE.Mesh( floorGeometry, floorMaterial );
     floor.translateX(length/2);
     floor.translateZ(width/2);
    //  floor.translateY(-0.05);
     floortexture.wrapS = THREE.RepeatWrapping;
     floortexture.wrapT = THREE.RepeatWrapping;
     floortexture.repeat.set( 100,100);
     floor.rotateX(Math.PI / 2);
    //  floor.position.y = -1;
    //  const gui = new GUI();
    //  const floorMaterialFolder = gui.addFolder('floorMaterial');
    //  floorMaterialFolder.addColor(floorMaterial,'color').name("地板颜色").onChange(function(value){
    //     floor.material.color.set(value);
    // });
    group.add(floor);
    return group;
     
}
