<template>
    <div class="maindiv">
        <div class="content-container">
            <!-- <dv-full-screen-container> -->
            <!-- <dv-border-box-11 title="智慧综合化维护">
        </dv-border-box-11> -->
            <div class="left" id="left">
                <dv-border-box-7 :color="['#214095', '#D3E1F8']">
                    <!-- <div class="title">数据</div> -->
                    <div class="left-one">
                        <el-select v-model="form.roomName" placeholder="请选所属机房" filterable clearable
                            @change="selectRoom" class="custom-select">
                            <el-option v-for="(item, index) in RmRoomList" :key="index" :label="item.roomName"
                                :value="item.uuid">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="left-two">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">PUE</span>
                        </div>
                    </div>
                    <div class="left-three">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">告警</span>
                        </div>
                        <div class="left-cont">
                            <el-row>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#F43A4A;">0</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#F08F48;">2</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#00FCB9;">4</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div class="left-four">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">容量管理</span>
                        </div>
                        <div class="capacity-chart-container">
                            <div class="capacity-chart" ref="capacityChart"></div>
                            <div class="capacity-stats">
                                <div class="stat-row">
                                    <span class="stat-label">总U数:</span>
                                    <span class="stat-value">{{ totalU }}U</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">已用:</span>
                                    <span class="stat-value used">{{ usedU }}U</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">剩余:</span>
                                    <span class="stat-value available">{{ availableU }}U</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">使用率:</span>
                                    <span class="stat-value usage">{{ usageRate }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </dv-border-box-7>

            </div>
            <div class="main">
                <div class="main-top">
                    <el-row justify="center">
                        <el-col :span="12">
                            <el-input placeholder="搜索" v-model="input" clearable class="custom-input"
                                style="width:98%;">
                            </el-input>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-search large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-arrow-left large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-full-screen large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-pie-chart large-red-icon" @click="toggleChartButtons"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-share large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-s-fold large-red-icon"></i>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="main-room">
                    <room3D @update-selected-cabinet="handleSelectedCabinetUpdate"
                            @update-RmRoomList="updateRmRoomList"
                            @update-RmCabinetModelList="updateRmCabinetModelList"
                            :show3DPattern="show3DPattern"
                            :selectedRoomId="form.roomName"
                            ref="room3D"
                    />
                </div>
                <!-- 详细数据按钮区域 -->
                <div class="chart-buttons" v-show="showChartButtons">
                    <div class="button-container">
                        <div class="chart-button" @click="handleDetailButton('temperature')">
                            <i class="el-icon-thermometer"></i>
                            <span>温度</span>
                        </div>
                        <div class="chart-button" @click="handleDetailButton('cooling')">
                            <i class="el-icon-ice-cream"></i>
                            <span>制冷</span>
                        </div>
                        <div class="chart-button" @click="handleDetailButton('weight')">
                            <i class="el-icon-scale"></i>
                            <span>承重</span>
                        </div>
                        <div class="chart-button" @click="handleDetailButton('space')">
                            <i class="el-icon-office-building"></i>
                            <span>空间</span>
                        </div>
                        <div class="chart-button" @click="handleDetailButton('power')">
                            <i class="el-icon-lightning"></i>
                            <span>电力</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right" id="right">
                <Editor :selectedCabinet = "this.selectedCabinet"
                @updateShow3DPattern="updateShow3DPattern"
                @switch-clicked="handleSwitchClicked"
                @switch-cleared="handleSwitchCleared"/>
            </div>

            <!-- 交换机信息面板 -->
            <MianBan />

            <!-- 槽位信息面板 -->
            <CaoWei />



            <!-- 当前日期+当前星期 -->
            <!-- <div class="datae">
            {{ nowDate + ' ' + nowWeek }}
        </div> -->
            <!-- 当前时间 -->
            <!-- <div class="timer">
            {{ nowTime }}
        </div> -->
        </div>
    </div>
</template>

<script>
// import room3D from "@/views/screen/index3d.vue";
import room3D from "@/views/screen/shiyan/internalper/ThreeScene.vue";
import { listRmRooms } from "@/api/room/RmRoom";
import { listRmCabinet } from "@/api/cabinet/RmCabinet";
import { listRmEquipment } from "@/api/equipment/RmEquipment";
import Editor from "./shiyan/internalper/Editor.vue";
import MianBan from "./shiyan/internalper/MianBan.vue";
import CaoWei from "./shiyan/internalper/CaoWei.vue";
import echarts from 'echarts';
export default {
    name: 'screenindex',
    components: { room3D, Editor, MianBan, CaoWei },
    data() {
        return {
            input: '',
            loadinge: false,
            // dialogTableVisible: false

            // 机房管理表格数据
            RmRoomList: [],
            // 表单参数
            form: {},
            selectedCabinet : null,
            index : null,
            RmCabinetModelList: [],
            RmRoomList: [],
            show3DPattern:false,
            showChartButtons: false,

            // 容量管理相关数据
            capacityChart: null,
            totalU: 0,
            usedU: 0,
            availableU: 0,
            usageRate: 0,
            cabinetList: [],
            equipmentList: [],

        }
    },
    mounted() {
        // this.currentTime()
        // this.getRoomList();
        this.$nextTick(() => {
            this.initCapacityChart();
            this.loadCapacityData();
        });
    },
    // 销毁定时器
    beforeDestroy() {
        // console.log("销毁定时器");
        // clearInterval(this.getDate) // 在Vue实例销毁前，清除时间定时器
        // clearInterval(this.monitortime)

        // 销毁图表实例
        if (this.capacityChart) {
            this.capacityChart.dispose();
            this.capacityChart = null;
        }
    },
    created() {
        this.getRoomList();
        this.loadCapacityData();
    },
    methods: {
        /** 查询机房管理列表 */
        selectRoom(selectedRoomId) {
            console.log('机房选择变化:', selectedRoomId);
            this.form.roomName = selectedRoomId;
            this.getRoomList();
            this.loadCapacityData();

            // 清除当前选中的机柜
            this.selectedCabinet = null;
        },
        getRoomList() {
            this.loading = true;
            listRmRooms().then(response => {
                this.RmRoomList = response.rows;
                if (this.RmRoomList.length > 0 && this.form.roomName==null) {
                    this.form.roomName = this.RmRoomList[0].uuid
                }
            });
        },
        handleSelectedCabinetUpdate(newCabinet) {
            this.selectedCabinet = newCabinet; // 更新 selectedCabinet 数据
            console.log('Selected Cabinet:', this.selectedCabinet);

        },
        updateRmCabinetModelList(RmCabinetModelList){
            this.RmCabinetModelList = RmCabinetModelList;
        },
        updateRmRoomList(RmRoomList){
            this.getRoomList = RmRoomList;
        },
        updateShow3DPattern(show3DPattern){
            this.show3DPattern = show3DPattern;
        },
        // 处理交换机点击事件
        handleSwitchClicked() {
            // 获取main元素
            const mainElement = document.querySelector('.main');
            if (mainElement) {
                // 将main元素向左平移20%
                mainElement.style.transform = 'translateX(-20%)';
            }
        },
        // 处理交换机清除事件
        handleSwitchCleared() {
            // 获取main元素
            const mainElement = document.querySelector('.main');
            if (mainElement) {
                // 恢复main元素位置
                mainElement.style.transform = 'translateX(0)';
            }
        },
        // 切换图表按钮显示状态
        toggleChartButtons() {
            this.showChartButtons = !this.showChartButtons;
        },
        // 处理详细数据按钮点击
        handleDetailButton(type) {
            // 调用ThreeScene.vue中的测试按钮函数
            if (this.$refs.room3D) {
                this.$refs.room3D.isMotorsVis();
            }
            console.log(`点击了${type}按钮`);
        },

        // 初始化容量管理图表
        initCapacityChart() {
            if (!this.$refs.capacityChart) return;

            this.capacityChart = echarts.init(this.$refs.capacityChart);
            this.updateCapacityChart();
        },

        // 更新容量管理图表
        updateCapacityChart() {
            if (!this.capacityChart) return;

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c}U ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    textStyle: {
                        color: '#389BDA',
                        fontSize: 12
                    },
                    itemWidth: 12,
                    itemHeight: 12
                },
                series: [
                    {
                        name: '容量使用情况',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['40%', '50%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '16',
                                fontWeight: 'bold',
                                color: '#389BDA'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {
                                value: this.usedU,
                                name: '已使用',
                                itemStyle: {
                                    color: '#F43A4A'
                                }
                            },
                            {
                                value: this.availableU,
                                name: '可用空间',
                                itemStyle: {
                                    color: '#00FCB9'
                                }
                            }
                        ]
                    }
                ]
            };

            this.capacityChart.setOption(option);
        },

        // 加载容量数据
        async loadCapacityData() {
            try {
                // 获取机柜列表
                const cabinetResponse = await listRmCabinet({
                    roomId: this.form.roomName
                });
                this.cabinetList = cabinetResponse.rows || [];

                // 获取设备列表
                const equipmentResponse = await listRmEquipment({
                    roomId: this.form.roomName
                });
                this.equipmentList = equipmentResponse.rows || [];

                // 计算容量数据
                this.calculateCapacityData();

                // 更新图表
                if (this.capacityChart) {
                    this.updateCapacityChart();
                }
            } catch (error) {
                console.error('加载容量数据失败:', error);
            }
        },

        // 计算容量数据
        calculateCapacityData() {
            // 计算总U数
            this.totalU = this.cabinetList.reduce((total, cabinet) => {
                return total + (parseInt(cabinet.totalU) || 0);
            }, 0);

            // 计算已使用U数
            this.usedU = this.equipmentList.reduce((total, equipment) => {
                return total + (parseInt(equipment.occupiedU) || 0);
            }, 0);

            // 计算可用U数
            this.availableU = this.totalU - this.usedU;

            // 计算使用率
            this.usageRate = this.totalU > 0 ? Math.round((this.usedU / this.totalU) * 100) : 0;
        },
        // updateSelectedCabinet(cabinetData,index) {
        // if(cabinetData && index){
        //   this.selectedCabinet = cabinetData;
        //   this.index = index;
        //   // console.log('this.selectedCabinet====',this.selectedCabinet);
        //   // console.log('this.index====',this.index);
        // }

    // },
}

}
</script>

<style lang="scss" scoped>
.maindiv {
    // width: 1920px;
    // height: 1080px;
    width: 100%;
    height: 100vh;
    // background-image: url('../../assets/images/datavbg.png');
    background: url('../../assets/images/screen/1666271024838094849.jpg');
    overflow: hidden;
    background-size: cover;
}

::v-deep .custom-input .el-input__inner {
    background-color: transparent;
    border-color: #389BDA;
}

//select样式
::v-deep .custom-select .el-input__inner {
    border: none;
    background-color: transparent;
    color: #389BDA;
    font-size: 18px;
    font-weight: bold;
}

::v-deep .custom-select .el-input.is-focus .el-input__inner {
    border: none;
    box-shadow: none;
}

::v-deep .custom-select .el-input__suffix .el-input__icon {
    color: #389BDA;
}

::v-deep .custom-select .el-select-dropdown__item {
    color: #389BDA;
    font-size: 20px;
    font-weight: bold;
}

::v-deep .custom-select .el-select-dropdown__item.selected,
::v-deep .custom-select .el-select-dropdown__item:hover {
    background-color: #389BDA;
    color: white;
}

/* 设置下拉列表的背景颜色 */
::v-deep .custom-select .el-select-dropdown {
    background-color: transparent;
    // background-color: rgba(255, 255, 255, 0.8); /* 这里可以根据需要调整透明度和颜色 */
}
::v-deep .el-col {
    padding-left: 5px;
    padding-right: 5px;
    box-sizing: border-box; /* 确保内边距不会影响元素的总宽度 */
}
.content-container {
    display: flex;
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

// .cons{
//   position: absolute;
//   //  border:1px solid red;
//    width: 100%;
//    height:calc(100% - 40px);
// }
.title {
    padding-top: 10px;
    padding-left: 10px;
    color: #fff;
    // color:#7EC699;
    // border:1px solid red;
    position: relative;
    align-items: center;
    // justify-content: center;
    font-weight: bold;
    // height:25px;
    // font-size:16px;
    //  border:1px solid red;
}

.titles {
    color: #fff;
    position: relative;
    align-items: center;
    text-align: center;
    height: 30px;
    width: 100%;
}

#left.hidden {
    opacity: 0;
    transform: translateX(-100%);
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: absolute; /* 使用绝对定位，不影响其他元素 */
    pointer-events: none; /* 隐藏时不接收鼠标事件 */
}

.left {
    width: 25%;
    // height: calc(100% - 60px);
    height: 100%;
    // position: absolute;
    top: 0px;
    left: 0px;
    position: relative; /* 必须设置定位 */
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    opacity: 1;
    transform: translateX(0);
    overflow: hidden; /* 防止内容溢出 */



    // border: 1px solid red;

    .left-one {
        height: 80px;
        // bottom:20px;
        // color: #389BDA;
        // border-radius: 3px;
        padding: 20px 20px 10px 20px;
        // align-items: center;
        // padding: 10px;

    }

    .left-two {
        height: 300px;
        // color: #389BDA;
        padding: 10px 20px;

    }

    .left-three {
        height: 200px;
        // color: #389BDA;
        padding: 10px 20px;

        .left-cont-box {
            width:100%;
            height: 100px;
            padding: 10px;
            // display: flex;
            // margin-top: 10px;
        }

        .left-cont-box-num {
            height: 70%;
            font-size: 28px;
            color: #fff;
            font-weight: bold;
            // border:1px solid red;
            display: flex;
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中（可选，若需要水平也居中则添加） */
        }

        .left-cont-box-title {
            font-size: 16px;
            color: #fff;
            font-weight: bold;
        }
    }

    .left-four {
        // height:250px;
        height: calc(100% - 630px);
        // color: #389BDA;
        padding: 10px 20px;

        .capacity-chart-container {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            height: calc(100% - 40px);

            .capacity-chart {
                flex: 1;
                min-height: 200px;
                width: 100%;
            }

            .capacity-stats {
                margin-top: 15px;
                padding: 10px;
                background: rgba(56, 155, 218, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(56, 155, 218, 0.3);

                .stat-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                    font-size: 12px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .stat-label {
                        color: #389BDA;
                        font-weight: normal;
                    }

                    .stat-value {
                        font-weight: bold;
                        color: #fff;

                        &.used {
                            color: #F43A4A;
                        }

                        &.available {
                            color: #00FCB9;
                        }

                        &.usage {
                            color: #F08F48;
                        }
                    }
                }
            }
        }
    }

    .left-cont {
        margin-top: 30px;
        text-align: center;
    }

    .title {
        height: 20px;
        display: flex;

        .title-line {
            height: 15px;
            width: 3px;
            background-color: #00FDBA;
        }

        .title-text {
            padding-left: 8px;
            color: #389BDA;
        }
    }
}

.text-center {
    text-align: center;
}

.main {
    width: 75%; /* 固定宽度，不使用flex */
    height: 100%;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1); /* 添加平滑过渡效果 */
    will-change: transform; /* 提示浏览器这些属性将会变化 */
    position: relative; /* 使用相对定位 */
    transform: translateX(0); /* 初始位置 */
    .main-top {
        padding: 10px;
        height: 60px;
        width: 50%;
        float: right;
        // background-color: transparent;
        .large-red-icon {
            font-size: 30px;
            color: #389BDA;
        }
    }
    .main-room {
        height: calc(100% - 80px); /* 为按钮区域留出空间 */
    }
}

.datae {
    font-size: 18px;
    position: absolute;
    top: 7px;
    left: 200px;
    color: #fff;
}

.timer {
    font-size: 18px;
    position: absolute;
    top: 7px;
    right: 200px;
    color: #fff;
}

/* 详细数据按钮样式 */
.chart-buttons {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;

    .button-container {
        display: flex;
        gap: 20px;
        background: rgba(0, 0, 0, 0.7);
        padding: 15px 25px;
        border-radius: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(56, 155, 218, 0.3);

        .chart-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            background: linear-gradient(135deg, rgba(56, 155, 218, 0.2), rgba(0, 252, 185, 0.1));
            border: 1px solid rgba(56, 155, 218, 0.5);
            border-radius: 12px;
            color: #389BDA;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;

            i {
                font-size: 20px;
                margin-bottom: 5px;
            }

            span {
                font-size: 12px;
                font-weight: bold;
                white-space: nowrap;
            }

            &:hover {
                background: linear-gradient(135deg, rgba(56, 155, 218, 0.4), rgba(0, 252, 185, 0.2));
                border-color: #00FDBA;
                color: #00FDBA;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 252, 185, 0.3);
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}
</style>
