<template>
    <div class="app-container">
      <!-- <button @click="isMotorsVis">测试按钮</button> -->
      <div ref="threeContainer" id = "threeContainer" ></div>
    </div>
  </template>
  
  <script>
  import { defineCameraPosition , defineCard, initRenderen,updateShow3DPattern, updateControlsTarget} from './Rendering/render.js'; // 确保路径正确
  import * as THREE from 'three';
  import { generateUUID } from 'three/src/math/MathUtils.js';
  import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
  import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
  import { initModel } from './model/allModel.js';
  import { modelVis , addInnerCube} from './Visual/modelVis.js'
  import Editor from './Editor.vue';
  export default {
    name: 'index',
    components: {
      Editor,
    },
    props: {
      show3DPattern: {
        type: <PERSON><PERSON>an,
      },
      selectedRoomId: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        innerContainer: true, // 初始状态为可见
        selectedCabinet: null, // 用于存储选中的机柜信息
        index : null,
        model : null, // 存储所有模型
        motor : null,
        // result: null, //存储所有模型
        // motor2: null, // 存储机柜外框
        // result2: null,
        card: null,
        obj: null,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          deptId: null,
          status: null,
          remarks: null,
          cabinetName: null,
          cabinetCode: null,
          roomId: null,
          roomName: null,
          totalU: null,
          manufacturer: null,
          cabinetType: null,
          length: null,
          width: null,
          height: null,
          positionX: null,
          positionY: null,
          orientation: null,
          modelId: null,
          modelName: null
        },
          // 机柜模板管理表格数据
          RmCabinetModelList: [],
          RmRoomList: [],
      };
    },
    mounted() {


    },
    
    async created() {
        // console.log('left ========',document.getElementById('left'));

        // 根据传入的机房ID获取数据
        await this.getList(this.selectedRoomId);
        await defineCameraPosition(this.RmRoomList);
        let model = await initModel(this.RmCabinetModelList, this.RmRoomList);
        this.$emit('update-RmCabinetModelList', this.RmCabinetModelList);
        this.$emit('update-RmRoomList', this.RmRoomList);

        if (!model || !model.getObjectByName("motor")) {
            console.error('Model initialization failed');
            return;
        }

        this.model = model;
        this.motor = model.getObjectByName("motor");
        this.obj = initRenderen(this.model, this.updateSelectedCabinet);
        console.log("RmCabinetModelList", this.RmCabinetModelList);
        console.log("RmRoomList", this.RmRoomList);
        console.log("初始化机房ID:", this.selectedRoomId);
    },
    watch: {
      innerContainer(newVal){
        if (this.motor) {
          modelVis(this.motor,newVal);
          addInnerCube(this.motor,newVal,this.RmCabinetModelList);
        } else {
          console.warn('Motor not initialized yet, skipping modelVis and addInnerCube calls');
        }
      },
      show3DPattern(newVal) {
        updateShow3DPattern(newVal);
      },
      selectedCabinet(newVal) {
        // console.log("newVal==",newVal);
        this.$emit('update-selected-cabinet', newVal); // 触发自定义事件
      },
      selectedRoomId(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          console.log('机房切换:', newVal);
          this.refreshRoomData(newVal);
        }
      },
    },
    methods: {
        async getList(roomId = null) {
            this.loading = true;
            try {
                const RmRoomList = await listRmRoom(this.queryParams);
                this.RmRoomList = RmRoomList.rows;
                this.total = RmRoomList.total;

                // 根据机房ID过滤机柜数据
                const cabinetQueryParams = { ...this.queryParams };
                if (roomId) {
                    cabinetQueryParams.roomId = roomId;
                }

                const RmCabinetModelList = await listRmCabinet(cabinetQueryParams);
                this.RmCabinetModelList = RmCabinetModelList.rows;
                this.total = RmCabinetModelList.total;

                console.log('获取机柜数据:', this.RmCabinetModelList.length, '个机柜，机房ID:', roomId);
            } catch (error) {
                console.error('Failed to fetch data:', error);
            } finally {
                this.loading = false;
            }
        },

        // 刷新机房数据
        async refreshRoomData(roomId) {
            try {
                // 重新获取机柜数据
                await this.getList(roomId);

                // 获取选中机房的详细信息
                const selectedRoom = this.RmRoomList.find(room => room.uuid === roomId);
                if (selectedRoom) {
                    console.log('切换到机房:', selectedRoom.roomName);
                }

                // 重新初始化3D模型
                await this.reinitializeModel();

            } catch (error) {
                console.error('刷新机房数据失败:', error);
            }
        },

        // 重新初始化3D模型
        async reinitializeModel() {
            try {
                // 清除当前选中的机柜
                this.selectedCabinet = null;

                // 清除旧的3D模型
                if (this.obj && this.obj.scene) {
                    // 清除场景中的所有模型
                    while(this.obj.scene.children.length > 0) {
                        this.obj.scene.remove(this.obj.scene.children[0]);
                    }
                }

                // 重新定义相机位置
                await defineCameraPosition(this.RmRoomList);

                // 重新初始化模型
                let model = await initModel(this.RmCabinetModelList, this.RmRoomList);
                this.$emit('update-RmCabinetModelList', this.RmCabinetModelList);
                this.$emit('update-RmRoomList', this.RmRoomList);

                if (!model || !model.getObjectByName("motor")) {
                    console.error('Model initialization failed');
                    return;
                }

                this.model = model;
                this.motor = model.getObjectByName("motor");

                // 重新初始化渲染器，但复用现有的容器
                this.obj = initRenderen(this.model, this.updateSelectedCabinet);

                // 更新控制中心点
                updateControlsTarget();

                console.log("重新初始化完成 - 机柜数量:", this.RmCabinetModelList.length);

            } catch (error) {
                console.error('重新初始化3D模型失败:', error);
            }
        },
    //   handleCardClick(event) {
    //     event.stopPropagation();
    //   },
    //   async getList() {
    //     this.loading = true;
    //     listRmRoom(this.queryParams).then(response => {
    //       this.RmRoomList = response.rows;
    //       this.total = response.total;
    //       this.loading = false;
    //     });
    //     listRmCabinet(this.queryParams).then(response => {
    //       this.RmCabinetModelList = response.rows;
    //       // console.log("RmCabinetModelList",this.RmCabinetModelList);
    //       // console.log("RmRoomList",this.RmRoomList);
    //       this.total = response.total;
    //       this.loading = false;
    //       this.initCard();
          
    //     });
    //   },
    //   initCard() {
    //     const compDiv = this.$refs.cardId;
    //     this.card = compDiv;
    //     const threeContainer = this.$refs.threeContainer;
    //     const miniContainer = this.$refs.miniContainer;
    //     const result = index(compDiv, this.updateSelectedCabinet,
    //     threeContainer,this.RmCabinetModelList,
    //     this.RmRoomList,miniContainer
    //   );
    //     this.result = result;
    //     this.motors = result.getObjectByName('motor').getObjectByName('jigui');
    //     // this.miniContainer = miniContainer;
    //     console.log('this.motor2',this.motor2);
        
    //   },
      updateSelectedCabinet(cabinetData,index) {
        if(cabinetData && index){
          this.selectedCabinet = cabinetData;
          this.index = index;
          // console.log('this.selectedCabinet====',this.selectedCabinet);
          // console.log('this.index====',this.index);
        }
        
        },
      isMotorsVis() {
        this.innerContainer = !this.innerContainer;
        
      },
        
    }
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep.app-container{
    padding:0px
  }
  /* 样式可以根据需要调整 */
  // .card {
  //   /* border: red 5px solid; */
  //   position: fixed;
  //   top: 85px;
  //   right: 0px;
  //   z-index: 1;
  //   height: 700px;
  //   width: 30%;
  //   position: absolute;
  //   display: flex;
  //   flex:row;
  //   color: rgb(253, 31, 31);
  //   background: rgb(113, 243, 178);
  //   opacity: 0; /* 初始透明度为0 */
  //   transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
  //   border:1px solid blue;
  // }
  // .miniContainer {
  //   /* border: blue 5px solid; */
  //   width: 350px;
  //   height: 400px;
  //   flex: 1;
  //   /* border:1px solid red; */
  // }
  // .card-content{
  //     /* border: red 5px solid; */
  //   flex:1;
  // }
  /* .abc {
    padding: 0px;
    margin: 0px;
    width: 100%;
  } */
  /* .lvse {
    height: 100vh;
    width: 100%;
    border: 1px solid red;
    transition: width 0.3s ease; /* 添加过渡效果 */
  /* } */
  </style>
  